name: autotools-macos
# autotools build of tesseract and training tools on macos homebrew and macports.
# run command line tests, basicapitest and unittests. '--disable-openmp'
on:
  #push:
  schedule:
    - cron: 0 20 * * *
  workflow_dispatch:
jobs:

  brew:
    runs-on: ${{ matrix.config.os }}
    strategy:
      fail-fast: false
      matrix:
        config:
          - { name: macos-latest-clang-autotools, os: macos-latest, cxx: clang++ }

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Get fonts, tessdata and langdata required for unit tests
      run: |
           git clone https://github.com/egorpugin/tessdata tessdata_unittest
           cp tessdata_unittest/fonts/* test/testing/
           mv tessdata_unittest/* ../

    - name: Install dependencies
      run: |
           brew install autoconf automake cabextract libtool
           brew install curl icu4c leptonica libarchive pango

    - name: Setup Tesseract
      run: |
           ./autogen.sh

    - name: Configure Tesseract
      run: |
           ./configure '--disable-shared' '--disable-openmp' '--disable-doc' '--with-pic' 'CXX=${{ matrix.config.cxx }}' 'CXXFLAGS=-g -O2'

    - name: Make and Install Tesseract
      run: |
           make -j 8
           sudo make install install
    - name: Make and Install Training Tools
      run: |
           make training -j 8
           sudo make install training-install

    - name: Make and run Unit Tests (clang)
      if: startsWith(matrix.config.cxx, 'clang')
      run: |
           make check

    - name: Make and run Unit Tests (unset LANG needed for g++-8, g++-9, g++-10 on macOS)
      if: startsWith(matrix.config.cxx, 'g')
      shell: bash
      run: |
           unset LANG LC_ALL LC_CTYPE
           locale
           make check

    - name: Display Version for tesseract, lstmtraining, text2image
      run: |
           tesseract -v
           lstmtraining -v
           text2image -v
      if: success() || failure()

    - name: List languages in different test tessdata-dir
      run: |
           tesseract  --list-langs --tessdata-dir ../tessdata
           tesseract  --list-langs --tessdata-dir ../tessdata_best
           tesseract  --list-langs --tessdata-dir ../tessdata_fast

    - name: Run Tesseract on test images in different languages
      run: |
           tesseract test/testing/phototest.tif - --oem 1  --tessdata-dir ../tessdata
           tesseract test/testing/raaj.tif - -l hin --oem 1   --tessdata-dir ../tessdata
           tesseract test/testing/viet.tif - -l vie --oem 1   --tessdata-dir ../tessdata
           tesseract test/testing/hebrew.png - -l heb --oem 1   --tessdata-dir ../tessdata
           tesseract test/testing/eurotext.tif - -l fra --oem 1 --tessdata-dir ../tessdata_best
           tesseract test/testing/arabic.tif - -l ara --oem 1 --psm 6  --tessdata-dir ../tessdata

    - name: Run Tesseract basicapitest
      run: |
           export "PKG_CONFIG_PATH=/usr/local/lib/pkgconfig"
           cd test
           ${{ matrix.config.cxx }} -o basicapitest testing/basicapitest.cpp $(pkg-config --cflags --libs tesseract lept) -pthread -std=c++17 -framework accelerate
           ./basicapitest

    - name: Display Compiler Version
      run: |
           ${{ matrix.config.cxx }} --version
           git log -3 --pretty=format:'%h %ad %s | %an'
      if: always()

    - name: Display Unit Tests Report
      run: |
           cat test-suite.log
      if: always()

# ============================================================================================

  ports:
    runs-on: ${{ matrix.config.os }}
    strategy:
      fail-fast: false
      matrix:
        config:
          - { name: macos-latest-clang-autotools, os: macos-latest, cxx: clang++ }

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Get fonts, tessdata and langdata required for tests
      run: |
           git clone https://github.com/egorpugin/tessdata tessdata_unittest
           cp tessdata_unittest/fonts/* test/testing/
           mv tessdata_unittest/* ../

    - name: Install Macports
      run: |
        curl -sSLO https://raw.githubusercontent.com/GiovanniBussi/macports-ci/master/macports-ci; source ./macports-ci install
        # --remove-brew does not remove the Homebrew entries in bin,
        # so remove them now.
        rm -v $(brew --prefix)/bin/*

    - name: Install Dependencies
      run: |
           sudo port install autoconf automake libtool pkgconfig
           sudo port install leptonica
           sudo port install cairo pango
           sudo port install icu +devel
           sudo port install cabextract libarchive curl

    - name: Setup Tesseract
      run: |
           ./autogen.sh

    - name: Configure Tesseract
      run: |
           ./configure  '--disable-shared' '--disable-openmp' '--disable-doc' '--with-pic' 'CXX=${{ matrix.config.cxx }}' 'CXXFLAGS=-g -O2'

    - name: Make and Install Tesseract
      run: |
           make -j 8
           sudo make install install

    - name: Make and Install Training Tools
      run: |
           make training -j 8
           sudo make install training-install

    - name: Make and run Unit Tests (clang)
      if: startsWith(matrix.config.cxx, 'clang')
      run: |
           make check

    - name: Display Version for tesseract, lstmtraining, text2image
      run: |
           tesseract -v
           lstmtraining -v
           text2image -v
      if: success() || failure()

    - name: List languages in different test tessdata-dir
      run: |
           tesseract  --list-langs --tessdata-dir ../tessdata
           tesseract  --list-langs --tessdata-dir ../tessdata_best
           tesseract  --list-langs --tessdata-dir ../tessdata_fast

    - name: Run Tesseract on test images in different languages
      run: |
           tesseract test/testing/phototest.tif - --oem 1  --tessdata-dir ../tessdata
           tesseract test/testing/raaj.tif - -l hin --oem 1   --tessdata-dir ../tessdata
           tesseract test/testing/viet.tif - -l vie --oem 1   --tessdata-dir ../tessdata
           tesseract test/testing/hebrew.png - -l heb --oem 1   --tessdata-dir ../tessdata
           tesseract test/testing/eurotext.tif - -l fra --oem 1 --tessdata-dir ../tessdata_best
           tesseract test/testing/arabic.tif - -l ara --oem 1 --psm 6  --tessdata-dir ../tessdata

    - name: Run Tesseract basicapitest
      run: |
           export "PKG_CONFIG_PATH=/usr/local/lib/pkgconfig"
           cd test
           ${{ matrix.config.cxx }} -o basicapitest testing/basicapitest.cpp -I/opt/local/include -L/opt/local/lib $(pkg-config --cflags --libs tesseract lept) -pthread -std=c++17 -framework Accelerate
           ./basicapitest

    - name: Display Compiler Version
      run: |
           ${{ matrix.config.cxx }} --version
           git log -3 --pretty=format:'%h %ad %s | %an'
      if: always()

    - name: Display Unit Tests Report
      run: |
           cat test-suite.log
      if: always()
