name: sw

on:
  schedule:
    # every 3rd day
    - cron: 0 0 */3 * *

jobs:
  build:
    runs-on: ${{ matrix.os }}
    container: ${{ matrix.container }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-2022, macos-latest]
        include:
          - os: ubuntu-22.04
            container: fedora:latest

    steps:
    - name: packages
      if: matrix.os == 'ubuntu-22.04'
      run: sudo dnf -y install cmake gcc lld which flex bison clang clang-tools-extra git

    - uses: actions/checkout@v4
      with:
        submodules: recursive
    - uses: egorpugin/sw-action@master

    - name: build
      if: github.event_name != 'pull_request' && (matrix.os == 'windows-2022')
      run: ./sw -static -shared -platform x86,x64 -config d,r build
    - name: build-pr
      if: github.event_name == 'pull_request' && (matrix.os == 'windows-2022')
      run: ./sw build

    - name: build
      if: github.event_name != 'pull_request' && (matrix.os != 'windows-2022')
      run: ./sw -static -shared -config d,r build -Dwith-tests=1
    - name: build-pr
      if: github.event_name == 'pull_request' && (matrix.os != 'windows-2022')
      run: ./sw build -Dwith-tests=1

    - name: download test data
      run: git clone https://github.com/egorpugin/tessdata tessdata_unittest

    - name: copy fonts
      if: matrix.os != 'windows-2022'
      run: cp tessdata_unittest/fonts/* test/testing/
    - name: copy fonts
      if: matrix.os == 'windows-2022'
      run: Copy-Item -Path "tessdata_unittest\fonts\*" -Destination "test\testing" -Recurse
      shell: pwsh

    - name: test
      if: github.event_name != 'pull_request' && (matrix.os != 'windows-2022' && matrix.os != 'macos-latest')
      run: ./sw -static -shared -config "d,r" test -Dwith-tests=1 "-Dskip-tests=lstm,lstm_recode"
      continue-on-error: true
    - name: test
      if: github.event_name == 'pull_request' && (matrix.os != 'windows-2022')
      run: ./sw test -Dwith-tests=1 "-Dskip-tests=lstm,lstm_recode"
      continue-on-error: true

    - name: test-nightly
      if: matrix.os != 'windows-2022' && matrix.os != 'macos-latest' && github.event.schedule=='0 0 * * *'
      run: ./sw -static -shared -config "d,r" test -Dwith-tests=1
      continue-on-error: true

    # windows and macos-latest tests hang here for some reason, investigate
    #- name: test
      #if: matrix.os == 'windows-2022' || matrix.os == 'macos-latest'
      #run: ./sw test -Dwith-tests=1 "-Dskip-tests=lstm,lstm_recode"
      #continue-on-error: true

    - name: Upload Unit Test Results
      if: always() && matrix.os != 'windows-2022'
      uses: actions/upload-artifact@v4
      with:
        name: Test Results (${{ matrix.os }})
        path: .sw/test/results.xml

    - name: Publish Test Report
      if: always() && matrix.os != 'windows-2022'
      uses: mikepenz/action-junit-report@v4
      with:
        check_name: test (${{ matrix.os }})
        report_paths: .sw/test/results.xml
        github_token: ${{ secrets.GITHUB_TOKEN }}
