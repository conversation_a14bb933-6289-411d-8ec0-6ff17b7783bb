/**
 * Simple example demonstrating how to use the Tesseract C++ API
 * This shows the core functionality of the Tesseract OCR engine
 */

#include <tesseract/baseapi.h>
#include <leptonica/allheaders.h>
#include <iostream>
#include <string>

int main() {
    // Initialize Tesseract API
    tesseract::TessBaseAPI *api = new tesseract::TessBaseAPI();
    
    // Initialize tesseract-ocr with English, without specifying tessdata path
    if (api->Init(NULL, "eng")) {
        fprintf(stderr, "Could not initialize tesseract.\n");
        exit(1);
    }
    
    // Example 1: OCR from image file
    std::cout << "=== Tesseract C++ API Example ===" << std::endl;
    std::cout << "Version: " << api->Version() << std::endl;
    
    // You would typically load an image like this:
    // Pix *image = pixRead("path/to/image.png");
    // api->SetImage(image);
    
    // For demonstration, let's create a simple text example
    const char* sample_text = "Hello World! This is a test.";
    api->SetVariable("tessedit_char_whitelist", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz ");
    
    std::cout << "\n=== Available Languages ===" << std::endl;
    std::vector<std::string> languages;
    api->GetAvailableLanguagesAsVector(&languages);
    for (const auto& lang : languages) {
        std::cout << "- " << lang << std::endl;
    }
    
    std::cout << "\n=== Page Segmentation Modes ===" << std::endl;
    std::cout << "Current PSM: " << api->GetPageSegMode() << std::endl;
    std::cout << "Available PSM modes:" << std::endl;
    std::cout << "  PSM_OSD_ONLY = 0" << std::endl;
    std::cout << "  PSM_AUTO_OSD = 1" << std::endl;
    std::cout << "  PSM_AUTO_ONLY = 2" << std::endl;
    std::cout << "  PSM_AUTO = 3 (default)" << std::endl;
    std::cout << "  PSM_SINGLE_COLUMN = 4" << std::endl;
    std::cout << "  PSM_SINGLE_BLOCK_VERT_TEXT = 5" << std::endl;
    std::cout << "  PSM_SINGLE_BLOCK = 6" << std::endl;
    std::cout << "  PSM_SINGLE_LINE = 7" << std::endl;
    std::cout << "  PSM_SINGLE_WORD = 8" << std::endl;
    std::cout << "  PSM_CIRCLE_WORD = 9" << std::endl;
    std::cout << "  PSM_SINGLE_CHAR = 10" << std::endl;
    std::cout << "  PSM_SPARSE = 11" << std::endl;
    std::cout << "  PSM_SPARSE_OSD = 12" << std::endl;
    std::cout << "  PSM_RAW_LINE = 13" << std::endl;
    
    std::cout << "\n=== OCR Engine Modes ===" << std::endl;
    std::cout << "Current OEM: " << api->oem() << std::endl;
    std::cout << "Available OEM modes:" << std::endl;
    std::cout << "  OEM_TESSERACT_ONLY = 0 (Legacy engine only)" << std::endl;
    std::cout << "  OEM_LSTM_ONLY = 1 (Neural nets LSTM engine only)" << std::endl;
    std::cout << "  OEM_TESSERACT_LSTM_COMBINED = 2 (Legacy + LSTM engines)" << std::endl;
    std::cout << "  OEM_DEFAULT = 3 (Default, based on what is available)" << std::endl;
    
    std::cout << "\n=== Configuration Variables ===" << std::endl;
    std::cout << "Some important configuration variables:" << std::endl;
    std::cout << "- tessedit_char_whitelist: Restrict characters to recognize" << std::endl;
    std::cout << "- tessedit_char_blacklist: Characters to ignore" << std::endl;
    std::cout << "- preserve_interword_spaces: Preserve spaces between words" << std::endl;
    std::cout << "- user_defined_dpi: Set DPI for input image" << std::endl;
    
    // Example of setting variables
    api->SetVariable("preserve_interword_spaces", "1");
    
    std::cout << "\n=== Data Path ===" << std::endl;
    std::cout << "Tessdata path: " << api->GetDatapath() << std::endl;
    
    // Clean up
    api->End();
    delete api;
    
    std::cout << "\n=== Example Complete ===" << std::endl;
    std::cout << "This example shows the basic structure of using Tesseract C++ API." << std::endl;
    std::cout << "To perform actual OCR, you would:" << std::endl;
    std::cout << "1. Load an image using Leptonica: Pix *image = pixRead(\"image.png\");" << std::endl;
    std::cout << "2. Set the image: api->SetImage(image);" << std::endl;
    std::cout << "3. Get OCR result: char* outText = api->GetUTF8Text();" << std::endl;
    std::cout << "4. Clean up: delete[] outText; pixDestroy(&image);" << std::endl;
    
    return 0;
}
