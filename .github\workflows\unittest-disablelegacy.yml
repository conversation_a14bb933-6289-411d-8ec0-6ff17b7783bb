name: unittest-disablelegacy
# autotools build on ubuntu, unittests with disabled legacy engine.
# currently some unittests are failing with disabled legacy engine.

on:
  #push:
  schedule:
    - cron: 0 10 * * *

jobs:
  linux:
    runs-on: ${{ matrix.os }}
    timeout-minutes: 150
    strategy:
      fail-fast: false
      matrix:
        compiler: [ g++, clang++-18 ]
        os: [ ubuntu-24.04 ]

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Install dependencies
      run: |
           sudo apt-get update
           sudo apt-get install autoconf libleptonica-dev libpango1.0-dev -y
           sudo apt-get install cabextract -y
           #sudo apt-get install libc++-7-dev libc++abi-7-dev -y

    - name: Setup
      run: |
           ./autogen.sh

    - name: Configure
      run: |
           ./configure '--disable-shared' '--disable-legacy' 'CXX=${{ matrix.compiler }}'

    - name: Make and Install Tesseract
      run: |
           make
           sudo make install install

    - name: Make and Install Training Tools
      run: |
           make training
           sudo make install training-install

    - name: Display Version
      run: |
           ${{ matrix.compiler }} --version
           tesseract -v
           lstmtraining -v
           text2image -v
      if: success() || failure()

    - name: Download fonts, tessdata and langdata required for tests
      run: |
           git clone https://github.com/egorpugin/tessdata tessdata_unittest
           cp tessdata_unittest/fonts/* test/testing/
           mv tessdata_unittest/* ../

    - name: Run Tesseract on phototest.tif and devatest.png
      run: |
           tesseract test/testing/phototest.tif -  --tessdata-dir ../tessdata
           tesseract test/testing/devatest.png - -l hin+eng  --tessdata-dir ../tessdata

    - name: Make and run Unit Tests
      run: |
           make check

    - name: Display Unit Tests Report
      run: |
           git log -3
           ${{ matrix.compiler }} --version
           cat test-suite.log
      if: always()
