name: unittest-macos
# autotools build on homebrew. unittests with address sanitizers. with openmp.
on:
  #push:
  schedule:
    - cron: 0 0 * * *

jobs:
  sanitizers:
    name: ${{ matrix.config.name }}
    runs-on: ${{ matrix.config.os }}
    strategy:
      fail-fast: false
      matrix:
        config:
          - { name: macos-arm-14-clang-unittest, os: macos-14, cxx: clang++ } # Apple silicon
          - { name: macos-latest-clang-unittest, os: macos-latest, cxx: clang++ }
          - { name: macos-latest-gcc-unittest, os: macos-latest, cxx: g++ }

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Install dependencies (macOS Homebrew)
      run: |
           brew install autoconf automake cabextract libtool
           brew install curl icu4c leptonica libarchive pango
    - name: Setup
      run: |
           ./autogen.sh

    - name: Configure (macOS Homebrew)
      run: |
           ./configure '--disable-shared' '--with-pic' \
                'CXX=${{ matrix.config.cxx }}' \
                'CXXFLAGS=-g -O2 -fsanitize=address,undefined'

    - name: Make and Install Tesseract
      run: |
           make
           sudo make install

    - name: Make and Install Training Tools
      run: |
           make training
           sudo make training-install

    - name: Display Tesseract and Training Tools Version
      run: |
           tesseract -v
           lstmtraining -v
           text2image -v
      if: success() || failure()

    - name: Download fonts, tessdata and langdata required for tests
      run: |
           git clone https://github.com/egorpugin/tessdata tessdata_unittest
           cp tessdata_unittest/fonts/* test/testing/
           mv tessdata_unittest/* ../

    - name: Run Tesseract on phototest.tif and devatest.png
      run: |
           tesseract test/testing/phototest.tif -  --tessdata-dir ../tessdata
           tesseract test/testing/devatest.png - -l hin+eng  --tessdata-dir ../tessdata

    - name: Make and run Unit Tests
      run: |
           make check

    - name: Display Unit Tests Report and compiler version
      run: |
           cat test-suite.log
           ${{ matrix.config.cxx }} --version
           git log -3 --pretty=format:'%h %ad %s | %an'
      if: always()
