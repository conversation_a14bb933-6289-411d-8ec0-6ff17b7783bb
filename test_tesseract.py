#!/usr/bin/env python3
"""
Test script to demonstrate Tesseract OCR functionality
Creates a simple test image with text and performs OCR on it
"""

from PIL import Image, ImageDraw, ImageFont
import pytesseract
import os

def create_test_image():
    """Create a simple test image with text"""
    # Create a white image
    width, height = 800, 200
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # Add some text
    text = "Hello World! This is a test for Tesseract OCR."
    
    try:
        # Try to use a system font
        font = ImageFont.truetype("arial.ttf", 40)
    except:
        # Fall back to default font
        font = ImageFont.load_default()
    
    # Calculate text position to center it
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    # Draw the text
    draw.text((x, y), text, fill='black', font=font)
    
    # Save the image
    image.save('test_image.png')
    print("✓ Test image created: test_image.png")
    return 'test_image.png'

def test_tesseract_ocr(image_path):
    """Test Tesseract OCR on the given image"""
    try:
        # Set the path to tesseract executable
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        
        # Perform OCR
        print(f"🔍 Performing OCR on {image_path}...")
        text = pytesseract.image_to_string(Image.open(image_path))
        
        print("📝 OCR Result:")
        print("-" * 50)
        print(text.strip())
        print("-" * 50)
        
        # Get additional information
        data = pytesseract.image_to_data(Image.open(image_path), output_type=pytesseract.Output.DICT)
        confidence_scores = [int(conf) for conf in data['conf'] if int(conf) > 0]
        
        if confidence_scores:
            avg_confidence = sum(confidence_scores) / len(confidence_scores)
            print(f"📊 Average confidence: {avg_confidence:.2f}%")
        
        return text.strip()
        
    except Exception as e:
        print(f"❌ Error during OCR: {e}")
        return None

def test_tesseract_command_line(image_path):
    """Test Tesseract using command line"""
    try:
        print(f"🖥️  Testing Tesseract command line on {image_path}...")
        
        # Run tesseract command
        os.system(f'tesseract "{image_path}" output_text')
        
        # Read the output
        if os.path.exists('output_text.txt'):
            with open('output_text.txt', 'r', encoding='utf-8') as f:
                text = f.read().strip()
            
            print("📝 Command line OCR Result:")
            print("-" * 50)
            print(text)
            print("-" * 50)
            
            # Clean up
            os.remove('output_text.txt')
            return text
        else:
            print("❌ Output file not created")
            return None
            
    except Exception as e:
        print(f"❌ Error during command line OCR: {e}")
        return None

def main():
    """Main function to run the tests"""
    print("🚀 Starting Tesseract OCR Test")
    print("=" * 60)
    
    # Create test image
    image_path = create_test_image()
    
    print("\n" + "=" * 60)
    print("📋 Test 1: Using pytesseract (Python wrapper)")
    print("=" * 60)
    
    # Test using pytesseract
    result1 = test_tesseract_ocr(image_path)
    
    print("\n" + "=" * 60)
    print("📋 Test 2: Using tesseract command line")
    print("=" * 60)
    
    # Test using command line
    result2 = test_tesseract_command_line(image_path)
    
    print("\n" + "=" * 60)
    print("✅ Test Summary")
    print("=" * 60)
    
    if result1:
        print("✓ Python wrapper test: SUCCESS")
    else:
        print("❌ Python wrapper test: FAILED")
    
    if result2:
        print("✓ Command line test: SUCCESS")
    else:
        print("❌ Command line test: FAILED")
    
    if result1 or result2:
        print("\n🎉 Tesseract OCR is working correctly!")
    else:
        print("\n❌ Tesseract OCR tests failed")
    
    # Clean up
    if os.path.exists(image_path):
        os.remove(image_path)
        print(f"🧹 Cleaned up: {image_path}")

if __name__ == "__main__":
    main()
